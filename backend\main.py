from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter
from sentence_transformers import SentenceTransformer
from pymilvus import connections, Collection
from typing import List, Dict
import requests
import uuid
import os
from simple_reranker import SimpleReranker

app = FastAPI()

# 允许跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化模型和Milvus连接
# 使用本地模型文件
embedding_model = SentenceTransformer('./models/bge-base-zh')
# 初始化重排器
reranker = SimpleReranker('./models/bge-base-zh')
MILVUS_HOST = "**********"
MILVUS_PORT = "19530"
MILVUS_USER = "kilo"
MILVUS_PWD = "abc123"
QWEN_ENDPOINT = "http://************:9103/v1"

# 连接Milvus
connections.connect(
    alias="default",
    host=MILVUS_HOST,
    port=MILVUS_PORT,
    user=MILVUS_USER,
    password=MILVUS_PWD
)

# 创建/获取Milvus集合
collection_name = "doc_chunks"
if not Collection(collection_name).exists():
    # 创建集合逻辑
    pass

collection = Collection(collection_name)

# 文本分块器
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=50
)

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    if file.content_type != 'application/pdf':
        raise HTTPException(status_code=400, detail="仅支持PDF文件")
    
    try:
        # 保存上传文件
        file_path = f"uploads/{uuid.uuid4()}.pdf"
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, "wb") as f:
            f.write(await file.read())
        
        # 解析PDF文本
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                text += page.extract_text() + "\n"
        
        # 文本分块
        chunks = text_splitter.split_text(text)
        
        # 生成嵌入向量
        embeddings = embedding_model.encode(chunks)
        
        # 存储到Milvus
        data = [
            [str(uuid.uuid4()) for _ in chunks],  # ids
            chunks,                                # 文本块
            embeddings.tolist(),                   # 向量
            [file.filename] * len(chunks)          # 来源
        ]
        collection.insert(data)
        collection.flush()
        
        return {"filename": file.filename, "chunks": len(chunks)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ask")
async def ask_question(query: Dict[str, str]):
    question = query.get("question")
    if not question:
        raise HTTPException(status_code=400, detail="问题不能为空")
    
    try:
        # 生成问题向量
        question_embedding = embedding_model.encode([question])[0].tolist()
        
        # Milvus向量检索
        search_params = {"metric_type": "L2", "params": {"nprobe": 16}}
        results = collection.search(
            [question_embedding],
            "embedding",
            search_params,
            limit=10,
            output_fields=["text"]
        )
        
        # 提取候选文本
        candidates = [hit.entity.get("text") for hit in results[0]]

        # 使用重排器进行重排
        reranked = reranker.rerank(question, candidates, top_k=3)
        
        # 构建Qwen请求
        context = "\n".join(reranked)
        prompt = f"基于以下上下文：\n{context}\n\n请回答：{question}"
        
        response = requests.post(
            f"{QWEN_ENDPOINT}/completions",
            json={"prompt": prompt, "max_tokens": 500}
        )
        
        if response.status_code != 200:
            raise Exception(f"模型API错误: {response.text}")
        
        return {"answer": response.json()["choices"][0]["text"]}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status")
async def get_status():
    """获取服务状态"""
    try:
        # 检查模型是否加载
        model_loaded = embedding_model is not None

        # 检查Milvus连接
        milvus_connected = True
        try:
            collection.num_entities  # 尝试访问集合
        except:
            milvus_connected = False

        return {
            "status": "running",
            "model_loaded": model_loaded,
            "model_path": "./models/bge-base-zh",
            "reranker_loaded": reranker is not None,
            "reranker_type": "SimpleReranker (embedding-based)",
            "milvus_connected": milvus_connected,
            "qwen_endpoint": QWEN_ENDPOINT
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"服务不可用: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)