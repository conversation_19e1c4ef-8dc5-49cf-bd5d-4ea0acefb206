"""
测试3：性能压力测试（50并发请求）
"""
import pytest
import requests
import time
import concurrent.futures
import statistics
from typing import List, Dict, Any

class TestPerformance:
    """性能测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, api_client, backend_url, wait_for_backend):
        """准备测试数据"""
        # 上传测试文档
        from tests.generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            upload_response = api_client.post(f"{backend_url}/upload", files=files)
        
        assert upload_response.status_code == 200, "测试数据上传失败"
        
        # 等待数据处理完成
        time.sleep(5)
        
        print("✅ 性能测试数据准备完成")
    
    def single_request(self, backend_url: str, question: str, request_id: int) -> Dict[str, Any]:
        """执行单个请求"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{backend_url}/ask",
                json={"question": question, "history": []},
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            return {
                "request_id": request_id,
                "status_code": response.status_code,
                "response_time": response_time,
                "success": response.status_code == 200,
                "error": None if response.status_code == 200 else response.text,
                "answer_length": len(response.json().get('answer', '')) if response.status_code == 200 else 0
            }
            
        except Exception as e:
            end_time = time.time()
            return {
                "request_id": request_id,
                "status_code": 0,
                "response_time": end_time - start_time,
                "success": False,
                "error": str(e),
                "answer_length": 0
            }
    
    def test_concurrent_requests_50(self, backend_url):
        """测试50个并发请求"""
        questions = [
            "什么是机器学习？",
            "深度学习有哪些应用？",
            "什么是Transformer架构？",
            "RAG系统是如何工作的？",
            "AI技术在医疗领域有什么应用？"
        ]
        
        # 准备50个请求
        requests_data = []
        for i in range(50):
            question = questions[i % len(questions)]
            requests_data.append((backend_url, question, i + 1))
        
        print(f"🚀 开始50并发请求测试...")
        start_time = time.time()
        
        # 使用线程池执行并发请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
            futures = [
                executor.submit(self.single_request, url, question, req_id)
                for url, question, req_id in requests_data
            ]
            
            results = []
            for future in concurrent.futures.as_completed(futures):
                results.append(future.result())
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        self.analyze_performance_results(results, total_time)
    
    def analyze_performance_results(self, results: List[Dict[str, Any]], total_time: float):
        """分析性能测试结果"""
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        success_rate = len(successful_requests) / len(results) * 100
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        else:
            avg_response_time = median_response_time = min_response_time = max_response_time = p95_response_time = 0
        
        requests_per_second = len(results) / total_time
        
        # 打印详细报告
        print("\n" + "="*60)
        print("📊 性能测试报告")
        print("="*60)
        print(f"总请求数: {len(results)}")
        print(f"成功请求数: {len(successful_requests)}")
        print(f"失败请求数: {len(failed_requests)}")
        print(f"成功率: {success_rate:.2f}%")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"请求速率: {requests_per_second:.2f} 请求/秒")
        print()
        print("响应时间统计:")
        print(f"  平均响应时间: {avg_response_time:.3f}秒")
        print(f"  中位数响应时间: {median_response_time:.3f}秒")
        print(f"  最小响应时间: {min_response_time:.3f}秒")
        print(f"  最大响应时间: {max_response_time:.3f}秒")
        print(f"  95%分位数: {p95_response_time:.3f}秒")
        
        if failed_requests:
            print("\n失败请求详情:")
            error_counts = {}
            for req in failed_requests:
                error_key = f"状态码{req['status_code']}" if req['status_code'] > 0 else "网络错误"
                error_counts[error_key] = error_counts.get(error_key, 0) + 1
            
            for error_type, count in error_counts.items():
                print(f"  {error_type}: {count}次")
        
        print("="*60)
        
        # 性能断言
        assert success_rate >= 80, f"成功率过低: {success_rate:.2f}% < 80%"
        assert avg_response_time <= 10, f"平均响应时间过长: {avg_response_time:.3f}s > 10s"
        assert p95_response_time <= 15, f"95%分位数响应时间过长: {p95_response_time:.3f}s > 15s"
        
        print("✅ 性能测试通过所有断言")
    
    def test_upload_performance(self, backend_url):
        """测试文件上传性能"""
        from tests.generate_test_pdf import generate_test_pdf
        
        def upload_file(request_id: int) -> Dict[str, Any]:
            start_time = time.time()
            
            try:
                pdf_path = generate_test_pdf()
                
                with open(pdf_path, 'rb') as f:
                    files = {'file': (f'test_doc_{request_id}.pdf', f, 'application/pdf')}
                    response = requests.post(f"{backend_url}/upload", files=files, timeout=60)
                
                end_time = time.time()
                
                return {
                    "request_id": request_id,
                    "status_code": response.status_code,
                    "response_time": end_time - start_time,
                    "success": response.status_code == 200,
                    "chunks": response.json().get('chunks', 0) if response.status_code == 200 else 0
                }
                
            except Exception as e:
                return {
                    "request_id": request_id,
                    "status_code": 0,
                    "response_time": time.time() - start_time,
                    "success": False,
                    "error": str(e),
                    "chunks": 0
                }
        
        print("🚀 开始文件上传性能测试（10个并发上传）...")
        
        # 10个并发上传
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(upload_file, i + 1) for i in range(10)]
            upload_results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        successful_uploads = [r for r in upload_results if r['success']]
        
        if successful_uploads:
            upload_times = [r['response_time'] for r in successful_uploads]
            avg_upload_time = statistics.mean(upload_times)
            
            print(f"✅ 上传性能测试完成:")
            print(f"   成功上传: {len(successful_uploads)}/10")
            print(f"   平均上传时间: {avg_upload_time:.2f}秒")
            
            assert len(successful_uploads) >= 8, "上传成功率过低"
            assert avg_upload_time <= 30, "平均上传时间过长"
        else:
            pytest.fail("所有上传都失败了")
