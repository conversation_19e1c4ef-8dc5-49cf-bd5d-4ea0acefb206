import React, { useState, useRef, useEffect } from 'react';
import { 
  Box, 
  TextField, 
  Button, 
  List, 
  ListItem, 
  ListItemText, 
  Avatar,
  Typography
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';

export default function ChatInterface({ onSendMessage }) {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    { text: '您好！请上传PDF文件后开始提问', sender: 'system' }
  ]);
  const messagesEndRef = useRef(null);

  const handleSend = () => {
    if (message.trim() === '') return;
    
    // 添加用户消息
    const newUserMessage = { text: message, sender: 'user' };
    const updatedMessages = [...messages, newUserMessage];
    setMessages(updatedMessages);
    setMessage('');
    
    // 调用父组件消息发送处理
    if (onSendMessage) onSendMessage(message);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '400px' }}>
      <Box sx={{ 
        flexGrow: 1, 
        overflowY: 'auto', 
        p: 2,
        border: '1px solid #e0e0e0', 
        borderRadius: '4px',
        mb: 2 
      }}>
        <List>
          {messages.map((msg, index) => (
            <ListItem key={index} alignItems="flex-start">
              <Avatar sx={{ 
                bgcolor: msg.sender === 'user' ? '#1976d2' : '#4caf50',
                mr: 2,
                width: 32,
                height: 32
              }}>
                {msg.sender === 'user' ? 'U' : 'A'}
              </Avatar>
              <ListItemText
                primary={msg.text}
                secondary={msg.sender === 'user' ? '您' : 'AI助手'}
                sx={{ 
                  bgcolor: msg.sender === 'user' ? '#e3f2fd' : '#e8f5e9',
                  p: 1,
                  borderRadius: '4px'
                }}
              />
            </ListItem>
          ))}
          <div ref={messagesEndRef} />
        </List>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <TextField
          fullWidth
          variant="outlined"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="输入您的问题..."
          multiline
          maxRows={4}
        />
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleSend}
          sx={{ ml: 1, height: '56px' }}
        >
          <SendIcon />
        </Button>
      </Box>
    </Box>
  );
}