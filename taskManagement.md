# RAG应用开发任务清单

## 系统架构设计
- [x] 完成系统架构设计
- [ ] 确定API接口规范

## 前端实现
- [x] 创建React应用框架
- [x] 实现文件上传组件
- [x] 构建聊天界面
- [x] 集成API请求功能

## 后端实现
- [x] 搭建FastAPI服务
- [x] 实现PDF解析模块
- [x] 实现文本分块逻辑
- [x] 集成BAAI/bge-base-zh嵌入模型
- [x] 实现Milvus存储/检索
- [x] 集成bge-reranker-base重排模型
- [x] 连接Qwen3-4B LLM

## 模型配置指南
- [x] 编写嵌入模型配置文档
- [x] 编写重排模型配置文档
- [ ] 编写Milvus连接指南
- [ ] 编写LLM调用说明

## 集成测试
- [ ] 端到端文件上传测试
- [ ] 问答功能测试
- [ ] 性能压力测试
- [ ] 测试1：上传PDF文件并验证Milvus存储
- [ ] 测试2：提问验证端到端RAG流程
- [ ] 测试3：压力测试（50并发请求）

### 前端任务完成情况
- [x] 使用Create React App初始化项目
- [x] 创建文件上传组件（支持PDF）
- [x] 创建聊天界面组件（消息列表+输入框）
- [x] 实现API调用：
  - [x] 文件上传接口：/upload
  - [x] 提问接口：/ask
- [x] 使用Material UI组件库