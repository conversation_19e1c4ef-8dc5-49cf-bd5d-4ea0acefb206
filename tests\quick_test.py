#!/usr/bin/env python3
"""
快速测试脚本 - 验证测试框架是否正常工作
"""
import os
import sys
import requests
import time

def test_pdf_generation():
    """测试PDF生成功能"""
    print("🔍 测试PDF文件生成...")
    try:
        from generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"✅ PDF文件生成成功: {pdf_path}")
            print(f"   文件大小: {file_size} 字节")
            return True
        else:
            print(f"❌ PDF文件未生成: {pdf_path}")
            return False
    except Exception as e:
        print(f"❌ PDF生成失败: {e}")
        return False

def test_backend_connection(backend_url="http://localhost:8000"):
    """测试后端连接"""
    print(f"🔍 测试后端连接: {backend_url}")
    try:
        response = requests.get(f"{backend_url}/status", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务连接成功")
            print(f"   响应: {response.json()}")
            return True
        else:
            print(f"❌ 后端服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def test_file_upload(backend_url="http://localhost:8000"):
    """测试文件上传功能"""
    print("🔍 测试文件上传功能...")
    
    # 确保有测试PDF文件
    pdf_path = "data/test_document.pdf"
    if not os.path.exists(pdf_path):
        print("   生成测试PDF文件...")
        from generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
    
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            response = requests.post(f"{backend_url}/upload", files=files, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文件上传成功")
            print(f"   文件名: {data.get('filename')}")
            print(f"   分块数: {data.get('chunks')}")
            return True
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文件上传异常: {e}")
        return False

def test_question_answer(backend_url="http://localhost:8000"):
    """测试问答功能"""
    print("🔍 测试问答功能...")
    
    question_data = {
        "question": "什么是机器学习？",
        "history": []
    }
    
    try:
        response = requests.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('answer', '')
            print("✅ 问答功能正常")
            print(f"   问题: {question_data['question']}")
            print(f"   回答: {answer[:100]}{'...' if len(answer) > 100 else ''}")
            return True
        else:
            print(f"❌ 问答请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 问答功能异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 RAG应用快速测试")
    print("=" * 50)
    
    # 创建必要目录
    os.makedirs("data", exist_ok=True)
    os.makedirs("reports", exist_ok=True)
    
    tests = [
        ("PDF生成", test_pdf_generation),
        ("后端连接", test_backend_connection),
        ("文件上传", test_file_upload),
        ("问答功能", test_question_answer),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试")
        print("-" * 30)
        
        try:
            if test_func():
                results.append((test_name, "✅ 通过"))
            else:
                results.append((test_name, "❌ 失败"))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, "❌ 异常"))
        
        time.sleep(1)  # 避免请求过快
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        print(f"{result} {test_name}")
        if "✅" in result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！测试框架工作正常。")
        print("💡 现在可以运行完整的集成测试:")
        print("   python run_tests.py")
    else:
        print(f"\n⚠️  有 {len(results)-passed} 个测试失败，请检查配置。")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
