
"""
基于嵌入模型的简化重排实现
"""
import torch
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

class SimpleReranker:
    """简化的重排器，使用嵌入模型计算相似度"""
    
    def __init__(self, model_path='./models/bge-base-zh'):
        self.model = SentenceTransformer(model_path)
        print(f"✅ 简化重排器初始化完成，使用模型: {model_path}")
    
    def rerank(self, query: str, passages: list, top_k: int = 3):
        """
        重排文档
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            top_k: 返回前k个结果
            
        Returns:
            重排后的文档列表
        """
        if not passages:
            return []
        
        # 生成查询和文档的嵌入
        query_embedding = self.model.encode([query])
        passage_embeddings = self.model.encode(passages)
        
        # 计算相似度
        similarities = cosine_similarity(query_embedding, passage_embeddings)[0]
        
        # 创建(文档, 分数)对并排序
        scored_passages = list(zip(passages, similarities))
        scored_passages.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前top_k个文档
        return [passage for passage, score in scored_passages[:top_k]]
    
    def get_scores(self, query: str, passages: list):
        """
        获取查询与文档的相似度分数
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            
        Returns:
            相似度分数列表
        """
        if not passages:
            return []
        
        query_embedding = self.model.encode([query])
        passage_embeddings = self.model.encode(passages)
        
        similarities = cosine_similarity(query_embedding, passage_embeddings)[0]
        return similarities.tolist()
