import React, { useState } from 'react';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { styled } from '@mui/material/styles';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export default function FileUpload({ onFileSelect }) {
  const [fileName, setFileName] = useState('');

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setFileName(file.name);
      onFileSelect(file);
    } else {
      alert('请选择PDF文件');
      event.target.value = '';
    }
  };

  return (
    <div style={{ margin: '20px 0' }}>
      <Button
        component="label"
        variant="contained"
        startIcon={<CloudUploadIcon />}
      >
        上传PDF文件
        <VisuallyHiddenInput 
          type="file" 
          accept=".pdf" 
          onChange={handleFileChange} 
        />
      </Button>
      {fileName && <p style={{ marginTop: '10px' }}>已选择: {fileName}</p>}
    </div>
  );
}