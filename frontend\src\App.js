import React, { useState } from 'react';
import FileUpload from './FileUpload';
import ChatInterface from './ChatInterface';
import { Container, Typography, Box } from '@mui/material';

function App() {
  const [file, setFile] = useState(null);
  const [conversation, setConversation] = useState([]);

  const handleFileSelect = (selectedFile) => {
    setFile(selectedFile);
    uploadFile(selectedFile);
  };

  const uploadFile = async (fileToUpload) => {
    const formData = new FormData();
    formData.append('file', fileToUpload);
    
    try {
      const response = await fetch('http://localhost:5000/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('文件上传失败');
      }
      
      const result = await response.json();
      setConversation([{ text: `文件 "${fileToUpload.name}" 上传成功，文档ID: ${result.document_id}`, sender: 'system' }]);
    } catch (error) {
      setConversation([{ text: `上传错误: ${error.message}`, sender: 'system' }]);
    }
  };

  const handleSendMessage = async (message) => {
    if (!file) {
      setConversation(prev => [...prev, 
        { text: '请先上传PDF文件', sender: 'system' }
      ]);
      return;
    }

    try {
      const response = await fetch('http://localhost:5000/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          document_id: file.name, // 实际应用中应使用服务器返回的document_id
          question: message
        }),
      });
      
      if (!response.ok) {
        throw new Error('请求失败');
      }
      
      const result = await response.json();
      setConversation(prev => [...prev, 
        { text: result.answer, sender: 'system' }
      ]);
    } catch (error) {
      setConversation(prev => [...prev, 
        { text: `提问错误: ${error.message}`, sender: 'system' }
      ]);
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom align="center">
        PDF智能助手
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <FileUpload onFileSelect={handleFileSelect} />
      </Box>
      
      <ChatInterface 
        onSendMessage={handleSendMessage} 
        messages={conversation}
      />
    </Container>
  );
}

export default App;
