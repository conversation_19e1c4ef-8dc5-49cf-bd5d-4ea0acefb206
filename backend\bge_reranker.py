"""
BGE重排模型实现
"""
import torch
from transformers import AutoTokenizer, AutoModel
import numpy as np
from typing import List, Tuple

class BGEReranker:
    """BGE重排模型封装类"""
    
    def __init__(self, model_path=None):
        """
        初始化重排模型

        Args:
            model_path: 模型路径
        """
        if model_path is None:
            # 默认路径
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            model_path = os.path.join(project_root, 'models', 'bge-reranker-base')

        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        self._load_model()
    
    def _load_model(self):
        """加载模型和tokenizer"""
        try:
            print(f"🔄 加载BGE重排模型: {self.model_path}")
            
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModel.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            
            print(f"✅ BGE重排模型加载成功")
            print(f"   设备: {self.device}")
            print(f"   参数量: {sum(p.numel() for p in self.model.parameters()):,}")
            
        except Exception as e:
            print(f"❌ BGE重排模型加载失败: {e}")
            raise e
    
    def compute_score(self, query: str, passage: str) -> float:
        """
        计算查询和文档的相关性分数
        
        Args:
            query: 查询文本
            passage: 文档文本
            
        Returns:
            相关性分数
        """
        try:
            # 对于BGE重排模型，需要特殊的输入格式
            # 通常是 [CLS] query [SEP] passage [SEP]
            inputs = self.tokenizer(
                query, passage,
                return_tensors="pt",
                truncation=True,
                max_length=512,
                padding=True
            ).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(**inputs)
                
                # BGE重排模型通常使用CLS token的输出
                # 如果有pooler_output就用，否则用last_hidden_state的第一个token
                if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                    score = outputs.pooler_output.squeeze()
                else:
                    score = outputs.last_hidden_state[:, 0, :]  # CLS token
                
                # 如果是多维tensor，需要进一步处理
                if score.dim() > 0:
                    # 对于重排任务，通常需要一个线性层将向量映射到标量分数
                    # 这里简化处理：取向量的平均值
                    score = score.mean()
                
                return score.cpu().item()
                
        except Exception as e:
            print(f"⚠️  计算分数时出错: {e}")
            # 返回一个默认分数
            return 0.0
    
    def rerank(self, query: str, passages: List[str], top_k: int = 3) -> List[str]:
        """
        重排文档
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            top_k: 返回前k个结果
            
        Returns:
            重排后的文档列表
        """
        if not passages:
            return []
        
        # 计算每个文档的分数
        scored_passages = []
        for passage in passages:
            score = self.compute_score(query, passage)
            scored_passages.append((passage, score))
        
        # 按分数降序排序
        scored_passages.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前top_k个文档
        return [passage for passage, score in scored_passages[:top_k]]
    
    def get_scores(self, query: str, passages: List[str]) -> List[float]:
        """
        获取查询与所有文档的相关性分数
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            
        Returns:
            分数列表
        """
        scores = []
        for passage in passages:
            score = self.compute_score(query, passage)
            scores.append(score)
        
        return scores
    
    def rerank_with_scores(self, query: str, passages: List[str], top_k: int = 3) -> List[Tuple[str, float]]:
        """
        重排文档并返回分数
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            top_k: 返回前k个结果
            
        Returns:
            (文档, 分数)元组列表
        """
        if not passages:
            return []
        
        # 计算每个文档的分数
        scored_passages = []
        for passage in passages:
            score = self.compute_score(query, passage)
            scored_passages.append((passage, score))
        
        # 按分数降序排序
        scored_passages.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前top_k个结果
        return scored_passages[:top_k]

def test_bge_reranker():
    """测试BGE重排器"""
    print("🧪 测试BGE重排器...")
    
    try:
        reranker = BGEReranker()
        
        query = "什么是机器学习？"
        passages = [
            "机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。",
            "深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的复杂表示。",
            "今天天气很好，阳光明媚。"
        ]
        
        print(f"\n查询: {query}")
        print(f"候选文档数: {len(passages)}")
        
        # 测试重排
        reranked = reranker.rerank(query, passages, top_k=2)
        scores = reranker.get_scores(query, passages)
        
        print(f"\n📊 重排结果:")
        for i, doc in enumerate(reranked):
            print(f"   排名{i+1}: {doc[:50]}...")
        
        print(f"\n📈 分数:")
        for i, (doc, score) in enumerate(zip(passages, scores)):
            print(f"   文档{i+1}: {score:.4f} - {doc[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ BGE重排器测试失败: {e}")
        return False

if __name__ == "__main__":
    test_bge_reranker()
