"""
测试配置文件
"""
import pytest
import requests
import time
import os
from typing import Dict, Any

# 测试配置
TEST_CONFIG = {
    "backend_url": "http://localhost:8000",
    "timeout": 30,
    "max_retries": 3,
    "test_pdf_path": "tests/data/test_document.pdf"
}

@pytest.fixture(scope="session")
def backend_url():
    """后端服务URL"""
    return TEST_CONFIG["backend_url"]

@pytest.fixture(scope="session")
def api_client(backend_url):
    """API客户端会话"""
    session = requests.Session()
    session.timeout = TEST_CONFIG["timeout"]
    return session

@pytest.fixture(scope="session")
def wait_for_backend(backend_url):
    """等待后端服务启动"""
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{backend_url}/status", timeout=5)
            if response.status_code == 200:
                print(f"✅ 后端服务已启动: {backend_url}")
                return True
        except requests.exceptions.RequestException:
            if attempt < max_attempts - 1:
                print(f"⏳ 等待后端服务启动... ({attempt + 1}/{max_attempts})")
                time.sleep(2)
            else:
                pytest.fail(f"❌ 后端服务启动超时: {backend_url}")
    return False

@pytest.fixture
def test_pdf_file():
    """测试用PDF文件路径"""
    pdf_path = TEST_CONFIG["test_pdf_path"]
    if not os.path.exists(pdf_path):
        pytest.skip(f"测试PDF文件不存在: {pdf_path}")
    return pdf_path

def pytest_configure(config):
    """pytest配置"""
    # 创建测试数据目录
    os.makedirs("tests/data", exist_ok=True)
    os.makedirs("tests/reports", exist_ok=True)

def pytest_html_report_title(report):
    """自定义HTML报告标题"""
    report.title = "RAG应用集成测试报告"
