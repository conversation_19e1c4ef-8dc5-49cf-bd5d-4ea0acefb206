"""
测试2：端到端RAG问答流程测试
"""
import pytest
import requests
import time
import json

class TestRAGQA:
    """RAG问答测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_test_data(self, api_client, backend_url, wait_for_backend):
        """为每个测试准备数据"""
        # 上传测试文档
        from tests.generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            upload_response = api_client.post(f"{backend_url}/upload", files=files)
        
        assert upload_response.status_code == 200, "测试数据上传失败"
        
        # 等待数据处理完成
        time.sleep(3)
        
        print("✅ 测试数据准备完成")
    
    def test_simple_question_answer(self, api_client, backend_url):
        """测试简单问答"""
        question_data = {
            "question": "什么是机器学习？",
            "history": []
        }
        
        response = api_client.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200, f"问答请求失败: {response.text}"
        
        data = response.json()
        assert 'answer' in data, "响应中缺少answer字段"
        assert len(data['answer']) > 0, "回答不能为空"
        
        print(f"✅ 问题: {question_data['question']}")
        print(f"✅ 回答: {data['answer'][:100]}...")
    
    def test_specific_content_question(self, api_client, backend_url):
        """测试针对文档特定内容的问题"""
        questions = [
            "深度学习有哪些应用？",
            "什么是Transformer架构？",
            "RAG系统是如何工作的？",
            "AI技术在医疗领域有什么应用？"
        ]
        
        for question in questions:
            question_data = {
                "question": question,
                "history": []
            }
            
            response = api_client.post(
                f"{backend_url}/ask",
                json=question_data,
                headers={"Content-Type": "application/json"}
            )
            
            assert response.status_code == 200, f"问题'{question}'请求失败: {response.text}"
            
            data = response.json()
            assert 'answer' in data, f"问题'{question}'响应中缺少answer字段"
            assert len(data['answer']) > 10, f"问题'{question}'的回答过短"
            
            print(f"✅ 问题: {question}")
            print(f"   回答: {data['answer'][:80]}...")
            
            # 避免请求过快
            time.sleep(1)
    
    def test_conversation_with_history(self, api_client, backend_url):
        """测试带历史记录的对话"""
        # 第一轮对话
        first_question = {
            "question": "什么是深度学习？",
            "history": []
        }
        
        response1 = api_client.post(
            f"{backend_url}/ask",
            json=first_question,
            headers={"Content-Type": "application/json"}
        )
        
        assert response1.status_code == 200, "第一轮对话失败"
        first_answer = response1.json()['answer']
        
        # 第二轮对话（带历史记录）
        second_question = {
            "question": "它有哪些具体应用？",
            "history": [
                {"role": "user", "content": "什么是深度学习？"},
                {"role": "assistant", "content": first_answer}
            ]
        }
        
        response2 = api_client.post(
            f"{backend_url}/ask",
            json=second_question,
            headers={"Content-Type": "application/json"}
        )
        
        assert response2.status_code == 200, "第二轮对话失败"
        second_answer = response2.json()['answer']
        
        print(f"✅ 第一轮 - 问题: {first_question['question']}")
        print(f"   回答: {first_answer[:80]}...")
        print(f"✅ 第二轮 - 问题: {second_question['question']}")
        print(f"   回答: {second_answer[:80]}...")
        
        assert len(second_answer) > 10, "第二轮回答过短"
    
    def test_empty_question(self, api_client, backend_url):
        """测试空问题"""
        question_data = {
            "question": "",
            "history": []
        }
        
        response = api_client.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 400, "空问题应该返回400错误"
        assert "问题不能为空" in response.json().get('detail', ''), "错误信息不正确"
        
        print("✅ 正确处理了空问题")
    
    def test_missing_question_field(self, api_client, backend_url):
        """测试缺少问题字段"""
        question_data = {
            "history": []
        }
        
        response = api_client.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 400, "缺少问题字段应该返回400错误"
        
        print("✅ 正确处理了缺少问题字段的情况")
    
    def test_invalid_json(self, api_client, backend_url):
        """测试无效JSON"""
        response = api_client.post(
            f"{backend_url}/ask",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422, "无效JSON应该返回422错误"
        
        print("✅ 正确处理了无效JSON")
    
    def test_long_question(self, api_client, backend_url):
        """测试长问题"""
        long_question = "请详细解释" + "人工智能技术的发展历程和未来趋势，" * 20
        
        question_data = {
            "question": long_question,
            "history": []
        }
        
        response = api_client.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"}
        )
        
        # 长问题应该能正常处理或返回合理错误
        assert response.status_code in [200, 400, 413], f"长问题处理异常: {response.status_code}"
        
        if response.status_code == 200:
            data = response.json()
            assert 'answer' in data, "长问题响应中缺少answer字段"
            print(f"✅ 长问题处理成功，问题长度: {len(long_question)}")
        else:
            print(f"✅ 长问题被正确拒绝，状态码: {response.status_code}")
    
    def test_question_about_nonexistent_content(self, api_client, backend_url):
        """测试关于文档中不存在内容的问题"""
        question_data = {
            "question": "请介绍量子计算机的工作原理和最新进展",
            "history": []
        }
        
        response = api_client.post(
            f"{backend_url}/ask",
            json=question_data,
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 200, "问题请求失败"
        
        data = response.json()
        answer = data['answer']
        
        print(f"✅ 不存在内容的问题: {question_data['question']}")
        print(f"   回答: {answer[:100]}...")
        
        # 系统应该能够回答，即使内容不在文档中
        assert len(answer) > 0, "应该有回答，即使内容不在文档中"
