# API 接口规范文档

## 1. 文件上传接口 (POST /upload)
### 请求格式
`multipart/form-data`
### 参数
| 参数名 | 类型   | 必填 | 说明         |
|--------|--------|------|--------------|
| file   | binary | 是   | PDF格式文件  |

### 成功响应
```json
{
  "status": "success",
  "file_id": "uuid字符串",
  "filename": "原始文件名",
  "chunks": "解析出的文本块数量"
}
```

### 错误响应
| 状态码 | 错误信息                  | 说明                     |
|--------|---------------------------|--------------------------|
| 400    | "仅支持PDF文件"           | 文件格式错误             |
| 500    | 具体错误信息              | 服务器内部处理错误       |
| 429    | "请求过于频繁"            | 超出速率限制             |

### 速率限制
- 最大频率：5次/分钟
- 超出限制返回429错误

---

## 2. 问答接口 (POST /ask)
### 请求格式
`application/json`
### 请求体
```json
{
  "question": "用户问题文本",
  "history": [
    {"role": "user", "content": "之前的问题"},
    {"role": "assistant", "content": "之前的回答"}
  ]
}
```

### 成功响应
```json
{
  "answer": "模型生成的回答文本"
}
```

### 错误响应
| 状态码 | 错误信息                  | 说明                     |
|--------|---------------------------|--------------------------|
| 400    | "问题不能为空"            | 问题参数缺失或为空       |
| 500    | 具体错误信息              | 模型服务或向量检索失败   |
| 429    | "请求过于频繁"            | 超出速率限制             |

### 速率限制
- 最大频率：10次/分钟
- 超出限制返回429错误

---

## 3. 服务状态检查 (GET /status)
### 请求格式
无参数

### 成功响应
```json
{
  "status": "running",
  "model_loaded": true,
  "milvus_connected": true,
  "qwen_endpoint": "http://10.122.83.52:9103/v1"
}
```

### 错误响应
| 状态码 | 错误信息                  | 说明                     |
|--------|---------------------------|--------------------------|
| 503    | "服务不可用"              | 关键组件未就绪           |

### 监控指标
- 服务启动时间
- 最近1分钟请求量
- 平均响应时间

---

## 全局规范
1. **认证机制**：所有API需要API Key认证
   - Header: `Authorization: Bearer <api_key>`
2. **版本管理**：API版本通过URL前缀管理 `/v1/...`
3. **请求ID**：每个响应包含唯一请求ID头 `X-Request-ID`