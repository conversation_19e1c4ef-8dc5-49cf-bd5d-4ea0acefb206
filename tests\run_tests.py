#!/usr/bin/env python3
"""
RAG应用集成测试运行脚本
"""
import os
import sys
import subprocess
import time
import requests
import argparse
from datetime import datetime

def check_backend_status(backend_url="http://localhost:8000", max_attempts=30):
    """检查后端服务状态"""
    print(f"🔍 检查后端服务状态: {backend_url}")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{backend_url}/status", timeout=5)
            if response.status_code == 200:
                print(f"✅ 后端服务运行正常")
                return True
        except requests.exceptions.RequestException:
            if attempt < max_attempts - 1:
                print(f"⏳ 等待后端服务启动... ({attempt + 1}/{max_attempts})")
                time.sleep(2)
            else:
                print(f"❌ 后端服务无法访问: {backend_url}")
                return False
    return False

def install_test_dependencies():
    """安装测试依赖"""
    print("📦 安装测试依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "tests/requirements.txt"
        ], check=True, capture_output=True, text=True)
        print("✅ 测试依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def generate_test_data():
    """生成测试数据"""
    print("📄 生成测试PDF文件...")
    try:
        from tests.generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        print(f"✅ 测试PDF文件生成完成: {pdf_path}")
        return True
    except Exception as e:
        print(f"❌ 测试PDF文件生成失败: {e}")
        return False

def run_tests(test_pattern=None, verbose=False, generate_report=True):
    """运行测试"""
    print("🧪 开始运行集成测试...")
    
    # 构建pytest命令
    cmd = [sys.executable, "-m", "pytest"]
    
    if test_pattern:
        cmd.append(f"tests/{test_pattern}")
    else:
        cmd.append("tests/")
    
    if verbose:
        cmd.append("-v")
    
    # 添加报告生成选项
    if generate_report:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_report = f"tests/reports/test_report_{timestamp}.html"
        json_report = f"tests/reports/test_report_{timestamp}.json"
        
        cmd.extend([
            f"--html={html_report}",
            "--self-contained-html",
            f"--json-report",
            f"--json-report-file={json_report}"
        ])
        
        print(f"📊 测试报告将保存到:")
        print(f"   HTML: {html_report}")
        print(f"   JSON: {json_report}")
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的错误回溯
        "--strict-markers",  # 严格的标记模式
        "-x"  # 遇到第一个失败就停止
    ])
    
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=".", capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ 所有测试通过!")
            return True
        else:
            print(f"❌ 测试失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RAG应用集成测试运行器")
    parser.add_argument("--test", "-t", help="指定测试模式 (upload|qa|performance|all)", default="all")
    parser.add_argument("--backend-url", "-u", help="后端服务URL", default="http://localhost:8000")
    parser.add_argument("--skip-backend-check", action="store_true", help="跳过后端服务检查")
    parser.add_argument("--skip-deps", action="store_true", help="跳过依赖安装")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--no-report", action="store_true", help="不生成测试报告")
    
    args = parser.parse_args()
    
    print("🎯 RAG应用集成测试")
    print("=" * 50)
    
    # 创建必要的目录
    os.makedirs("tests/data", exist_ok=True)
    os.makedirs("tests/reports", exist_ok=True)
    
    # 安装依赖
    if not args.skip_deps:
        if not install_test_dependencies():
            sys.exit(1)
    
    # 检查后端服务
    if not args.skip_backend_check:
        if not check_backend_status(args.backend_url):
            print("💡 提示: 请确保后端服务正在运行，或使用 --skip-backend-check 跳过检查")
            sys.exit(1)
    
    # 生成测试数据
    if not generate_test_data():
        sys.exit(1)
    
    # 确定要运行的测试
    test_patterns = {
        "upload": "test_01_file_upload.py",
        "qa": "test_02_rag_qa.py", 
        "performance": "test_03_performance.py",
        "all": None  # 运行所有测试
    }
    
    test_pattern = test_patterns.get(args.test)
    if args.test not in test_patterns:
        print(f"❌ 未知的测试模式: {args.test}")
        print(f"可用模式: {', '.join(test_patterns.keys())}")
        sys.exit(1)
    
    # 运行测试
    success = run_tests(
        test_pattern=test_pattern,
        verbose=args.verbose,
        generate_report=not args.no_report
    )
    
    if success:
        print("\n🎉 集成测试完成!")
        sys.exit(0)
    else:
        print("\n💥 集成测试失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
