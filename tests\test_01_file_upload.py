"""
测试1：PDF文件上传并验证Milvus存储
"""
import pytest
import requests
import os
import time
from pymilvus import connections, Collection

class TestFileUpload:
    """文件上传测试类"""
    
    def test_upload_pdf_success(self, api_client, backend_url, wait_for_backend):
        """测试成功上传PDF文件"""
        # 生成测试PDF文件
        from tests.generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        
        # 准备上传文件
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            
            # 发送上传请求
            response = api_client.post(f"{backend_url}/upload", files=files)
        
        # 验证响应
        assert response.status_code == 200, f"上传失败: {response.text}"
        
        data = response.json()
        assert 'filename' in data, "响应中缺少filename字段"
        assert 'chunks' in data, "响应中缺少chunks字段"
        assert data['filename'] == 'test_document.pdf', "文件名不匹配"
        assert data['chunks'] > 0, "文本块数量应大于0"
        
        print(f"✅ PDF上传成功: {data['filename']}, 分块数量: {data['chunks']}")
    
    def test_upload_non_pdf_file(self, api_client, backend_url, wait_for_backend):
        """测试上传非PDF文件应该失败"""
        # 创建临时文本文件
        txt_path = "tests/data/test.txt"
        os.makedirs(os.path.dirname(txt_path), exist_ok=True)
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文本文件")
        
        try:
            with open(txt_path, 'rb') as f:
                files = {'file': ('test.txt', f, 'text/plain')}
                response = api_client.post(f"{backend_url}/upload", files=files)
            
            # 应该返回400错误
            assert response.status_code == 400, "应该拒绝非PDF文件"
            assert "仅支持PDF文件" in response.json().get('detail', ''), "错误信息不正确"
            
            print("✅ 正确拒绝了非PDF文件")
        finally:
            # 清理临时文件
            if os.path.exists(txt_path):
                os.remove(txt_path)
    
    def test_upload_empty_file(self, api_client, backend_url, wait_for_backend):
        """测试上传空文件"""
        # 创建空PDF文件
        empty_pdf_path = "tests/data/empty.pdf"
        os.makedirs(os.path.dirname(empty_pdf_path), exist_ok=True)
        with open(empty_pdf_path, 'wb') as f:
            f.write(b'')  # 空文件
        
        try:
            with open(empty_pdf_path, 'rb') as f:
                files = {'file': ('empty.pdf', f, 'application/pdf')}
                response = api_client.post(f"{backend_url}/upload", files=files)
            
            # 应该返回500错误（PDF解析失败）
            assert response.status_code == 500, "空文件应该导致处理错误"
            
            print("✅ 正确处理了空文件错误")
        finally:
            # 清理临时文件
            if os.path.exists(empty_pdf_path):
                os.remove(empty_pdf_path)
    
    def test_verify_milvus_storage(self, api_client, backend_url, wait_for_backend):
        """验证文件内容是否正确存储到Milvus"""
        # 首先上传文件
        from tests.generate_test_pdf import generate_test_pdf
        pdf_path = generate_test_pdf()
        
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            upload_response = api_client.post(f"{backend_url}/upload", files=files)
        
        assert upload_response.status_code == 200, "文件上传失败"
        chunks_count = upload_response.json()['chunks']
        
        # 等待数据写入Milvus
        time.sleep(2)
        
        # 连接Milvus验证数据
        try:
            # 使用与后端相同的连接参数
            connections.connect(
                alias="test",
                host="**********",
                port="19530",
                user="kilo",
                password="abc123"
            )
            
            collection = Collection("doc_chunks", using="test")
            collection.load()
            
            # 查询集合中的数据数量
            count = collection.num_entities
            print(f"✅ Milvus中存储的向量数量: {count}")
            
            # 验证数据确实被存储
            assert count >= chunks_count, f"Milvus中的数据量({count})少于预期的分块数({chunks_count})"
            
            # 执行简单的向量搜索验证
            search_params = {"metric_type": "L2", "params": {"nprobe": 16}}
            
            # 使用一个简单的查询向量（768维零向量）
            query_vector = [[0.0] * 768]
            
            results = collection.search(
                query_vector,
                "embedding",
                search_params,
                limit=5,
                output_fields=["text"]
            )
            
            assert len(results[0]) > 0, "搜索应该返回结果"
            print(f"✅ 向量搜索成功，返回{len(results[0])}个结果")
            
        except Exception as e:
            pytest.skip(f"无法连接Milvus进行验证: {str(e)}")
        finally:
            try:
                connections.disconnect("test")
            except:
                pass
    
    def test_multiple_file_upload(self, api_client, backend_url, wait_for_backend):
        """测试多个文件上传"""
        from tests.generate_test_pdf import generate_test_pdf
        
        upload_results = []
        
        # 上传3个文件
        for i in range(3):
            pdf_path = generate_test_pdf()
            
            with open(pdf_path, 'rb') as f:
                files = {'file': (f'test_document_{i}.pdf', f, 'application/pdf')}
                response = api_client.post(f"{backend_url}/upload", files=files)
            
            assert response.status_code == 200, f"第{i+1}个文件上传失败"
            upload_results.append(response.json())
            
            # 避免上传过快
            time.sleep(1)
        
        # 验证所有上传都成功
        total_chunks = sum(result['chunks'] for result in upload_results)
        print(f"✅ 成功上传{len(upload_results)}个文件，总分块数: {total_chunks}")
        
        assert len(upload_results) == 3, "应该成功上传3个文件"
        assert total_chunks > 0, "总分块数应大于0"
