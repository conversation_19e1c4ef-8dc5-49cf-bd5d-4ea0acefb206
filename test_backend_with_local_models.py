#!/usr/bin/env python3
"""
测试使用本地模型的后端服务
"""
import requests
import time
import json
import os

def test_backend_status(backend_url="http://localhost:8000"):
    """测试后端状态"""
    print("🔍 测试后端服务状态...")
    
    try:
        response = requests.get(f"{backend_url}/status", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端服务状态正常")
            print(f"   服务状态: {data.get('status')}")
            print(f"   模型加载: {data.get('model_loaded')}")
            print(f"   模型路径: {data.get('model_path')}")
            print(f"   重排器加载: {data.get('reranker_loaded')}")
            print(f"   重排器类型: {data.get('reranker_type')}")
            print(f"   Milvus连接: {data.get('milvus_connected')}")
            print(f"   Qwen端点: {data.get('qwen_endpoint')}")
            return True
        else:
            print(f"❌ 后端服务状态异常: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False

def test_file_upload(backend_url="http://localhost:8000"):
    """测试文件上传功能"""
    print("\n🔍 测试文件上传功能...")
    
    # 生成测试PDF
    pdf_path = "tests/data/test_document.pdf"
    if not os.path.exists(pdf_path):
        print("   生成测试PDF文件...")
        try:
            import sys
            sys.path.append('tests')
            from generate_test_pdf import generate_test_pdf
            pdf_path = generate_test_pdf()
        except Exception as e:
            print(f"❌ 无法生成测试PDF: {e}")
            return False
    
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test_document.pdf', f, 'application/pdf')}
            response = requests.post(f"{backend_url}/upload", files=files, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文件上传成功")
            print(f"   文件名: {data.get('filename')}")
            print(f"   分块数: {data.get('chunks')}")
            return True
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 文件上传异常: {e}")
        return False

def test_question_answer(backend_url="http://localhost:8000"):
    """测试问答功能"""
    print("\n🔍 测试问答功能...")
    
    test_questions = [
        "什么是机器学习？",
        "深度学习有哪些应用？",
        "什么是Transformer架构？"
    ]
    
    success_count = 0
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n   测试问题 {i}: {question}")
        
        question_data = {
            "question": question,
            "history": []
        }
        
        try:
            response = requests.post(
                f"{backend_url}/ask",
                json=question_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('answer', '')
                print(f"   ✅ 回答: {answer[:100]}{'...' if len(answer) > 100 else ''}")
                success_count += 1
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   错误: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n📊 问答测试结果: {success_count}/{len(test_questions)} 成功")
    return success_count == len(test_questions)

def test_reranker_functionality(backend_url="http://localhost:8000"):
    """测试重排功能"""
    print("\n🔍 测试重排功能...")
    
    # 测试相关性不同的问题
    test_cases = [
        {
            "question": "什么是深度学习？",
            "expected_keywords": ["深度学习", "神经网络", "机器学习"]
        },
        {
            "question": "AI在医疗领域的应用？", 
            "expected_keywords": ["医疗", "AI", "人工智能", "应用"]
        }
    ]
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        question = case["question"]
        expected_keywords = case["expected_keywords"]
        
        print(f"\n   测试用例 {i}: {question}")
        
        question_data = {
            "question": question,
            "history": []
        }
        
        try:
            response = requests.post(
                f"{backend_url}/ask",
                json=question_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('answer', '')
                
                # 检查答案是否包含相关关键词
                keyword_found = any(keyword in answer for keyword in expected_keywords)
                
                if keyword_found:
                    print(f"   ✅ 重排效果良好，答案相关性高")
                    print(f"   回答: {answer[:80]}...")
                    success_count += 1
                else:
                    print(f"   ⚠️  答案相关性可能不够高")
                    print(f"   回答: {answer[:80]}...")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(1)
    
    print(f"\n📊 重排测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count >= len(test_cases) // 2  # 至少一半成功

def main():
    """主函数"""
    print("🎯 本地模型后端服务测试")
    print("="*50)
    
    backend_url = "http://localhost:8000"
    
    tests = [
        ("服务状态", test_backend_status),
        ("文件上传", test_file_upload),
        ("问答功能", test_question_answer),
        ("重排功能", test_reranker_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试")
        print("-" * 30)
        
        try:
            success = test_func(backend_url)
            results.append((test_name, "✅ 通过" if success else "❌ 失败"))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, "❌ 异常"))
    
    # 输出总结
    print("\n" + "="*50)
    print("📊 测试结果总结")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        print(f"{result} {test_name}")
        if "✅" in result:
            passed += 1
    
    print(f"\n通过率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！本地模型配置成功。")
        print("💡 现在可以:")
        print("   1. 使用前端界面进行交互")
        print("   2. 运行完整的集成测试")
        print("   3. 部署到生产环境")
    elif passed >= len(results) // 2:
        print(f"\n✅ 大部分测试通过，系统基本可用。")
        print("💡 建议检查失败的测试项目并进行优化。")
    else:
        print(f"\n⚠️  多个测试失败，请检查配置。")
        print("💡 建议:")
        print("   1. 确认后端服务正在运行")
        print("   2. 检查模型文件是否正确放置")
        print("   3. 验证Milvus和Qwen服务连接")
    
    return passed >= len(results) // 2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
