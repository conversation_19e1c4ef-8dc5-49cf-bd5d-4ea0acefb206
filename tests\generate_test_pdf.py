"""
生成测试用PDF文件
"""
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import os

def generate_test_pdf():
    """生成包含测试内容的PDF文件"""
    
    # 确保目录存在
    os.makedirs("tests/data", exist_ok=True)
    pdf_path = "tests/data/test_document.pdf"
    
    # 创建PDF文档
    doc = SimpleDocTemplate(pdf_path, pagesize=letter)
    styles = getSampleStyleSheet()
    
    # 自定义样式
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
    )
    
    content_style = ParagraphStyle(
        'CustomContent',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=12,
    )
    
    # 文档内容
    story = []
    
    # 标题
    story.append(Paragraph("人工智能技术发展报告", title_style))
    story.append(Spacer(1, 12))
    
    # 第一章：机器学习基础
    story.append(Paragraph("第一章：机器学习基础", styles['Heading2']))
    story.append(Paragraph(
        "机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。"
        "机器学习算法通过分析大量数据来识别模式，并使用这些模式来对新数据进行预测或决策。"
        "主要的机器学习类型包括监督学习、无监督学习和强化学习。",
        content_style
    ))
    
    story.append(Paragraph(
        "监督学习使用标记的训练数据来学习输入和输出之间的映射关系。常见的监督学习算法包括"
        "线性回归、决策树、随机森林和支持向量机。这些算法在分类和回归任务中表现出色。",
        content_style
    ))
    
    # 第二章：深度学习
    story.append(Paragraph("第二章：深度学习革命", styles['Heading2']))
    story.append(Paragraph(
        "深度学习是机器学习的一个子领域，它使用多层神经网络来学习数据的复杂表示。"
        "深度学习在图像识别、自然语言处理和语音识别等领域取得了突破性进展。"
        "卷积神经网络（CNN）特别适合处理图像数据，而循环神经网络（RNN）和Transformer"
        "架构在序列数据处理方面表现优异。",
        content_style
    ))
    
    story.append(Paragraph(
        "Transformer架构的出现彻底改变了自然语言处理领域。BERT、GPT等预训练模型"
        "展示了大规模语言模型的强大能力。这些模型通过在大量文本数据上进行预训练，"
        "学习到了丰富的语言知识，然后可以通过微调适应各种下游任务。",
        content_style
    ))
    
    # 第三章：应用场景
    story.append(Paragraph("第三章：实际应用场景", styles['Heading2']))
    story.append(Paragraph(
        "人工智能技术在各个行业都有广泛应用。在医疗领域，AI可以辅助医生进行疾病诊断"
        "和治疗方案制定。在金融领域，AI用于风险评估、欺诈检测和算法交易。"
        "在自动驾驶领域，AI技术使车辆能够感知环境并做出安全的驾驶决策。",
        content_style
    ))
    
    story.append(Paragraph(
        "检索增强生成（RAG）是一种新兴的AI应用模式，它结合了信息检索和文本生成技术。"
        "RAG系统首先从知识库中检索相关信息，然后使用这些信息来生成更准确、更有根据的回答。"
        "这种方法在问答系统、智能客服和知识管理等场景中表现出色。",
        content_style
    ))
    
    # 第四章：未来展望
    story.append(Paragraph("第四章：未来发展趋势", styles['Heading2']))
    story.append(Paragraph(
        "人工智能技术将继续快速发展。多模态AI能够同时处理文本、图像、音频等多种数据类型。"
        "联邦学习技术使得在保护隐私的前提下进行分布式机器学习成为可能。"
        "量子机器学习有望在特定问题上提供指数级的性能提升。",
        content_style
    ))
    
    story.append(Paragraph(
        "随着AI技术的普及，我们也需要关注AI伦理和安全问题。确保AI系统的公平性、"
        "透明性和可解释性将是未来发展的重要方向。同时，建立完善的AI治理框架"
        "对于促进AI技术的健康发展至关重要。",
        content_style
    ))
    
    # 构建PDF
    doc.build(story)
    print(f"✅ 测试PDF文件已生成: {pdf_path}")
    return pdf_path

if __name__ == "__main__":
    generate_test_pdf()
