"""
修复重排模型的tokenizer问题
"""
import os
import json
import shutil
from transformers import AutoTokenizer, AutoModel

def download_missing_tokenizer_files():
    """下载缺失的tokenizer文件"""
    print("🔧 修复重排模型tokenizer文件...")
    
    try:
        # 尝试从Hugging Face下载完整的tokenizer文件
        print("📥 从Hugging Face下载完整的tokenizer文件...")
        tokenizer = AutoTokenizer.from_pretrained('BAAI/bge-reranker-base')
        
        # 保存到本地目录
        local_path = './models/bge-reranker-base'
        print(f"💾 保存tokenizer文件到: {local_path}")
        tokenizer.save_pretrained(local_path)
        
        print("✅ tokenizer文件修复完成")
        return True
        
    except Exception as e:
        print(f"❌ tokenizer文件下载失败: {e}")
        return False

def create_alternative_reranker():
    """创建替代的重排实现"""
    print("🔧 创建替代重排实现...")
    
    reranker_code = '''
"""
基于嵌入模型的简化重排实现
"""
import torch
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

class SimpleReranker:
    """简化的重排器，使用嵌入模型计算相似度"""
    
    def __init__(self, model_path='./models/bge-base-zh'):
        self.model = SentenceTransformer(model_path)
        print(f"✅ 简化重排器初始化完成，使用模型: {model_path}")
    
    def rerank(self, query: str, passages: list, top_k: int = 3):
        """
        重排文档
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            top_k: 返回前k个结果
            
        Returns:
            重排后的文档列表
        """
        if not passages:
            return []
        
        # 生成查询和文档的嵌入
        query_embedding = self.model.encode([query])
        passage_embeddings = self.model.encode(passages)
        
        # 计算相似度
        similarities = cosine_similarity(query_embedding, passage_embeddings)[0]
        
        # 创建(文档, 分数)对并排序
        scored_passages = list(zip(passages, similarities))
        scored_passages.sort(key=lambda x: x[1], reverse=True)
        
        # 返回前top_k个文档
        return [passage for passage, score in scored_passages[:top_k]]
    
    def get_scores(self, query: str, passages: list):
        """
        获取查询与文档的相似度分数
        
        Args:
            query: 查询文本
            passages: 候选文档列表
            
        Returns:
            相似度分数列表
        """
        if not passages:
            return []
        
        query_embedding = self.model.encode([query])
        passage_embeddings = self.model.encode(passages)
        
        similarities = cosine_similarity(query_embedding, passage_embeddings)[0]
        return similarities.tolist()
'''
    
    # 保存重排器代码
    with open('backend/simple_reranker.py', 'w', encoding='utf-8') as f:
        f.write(reranker_code)
    
    print("✅ 简化重排器创建完成: backend/simple_reranker.py")
    return True

def test_fixed_models():
    """测试修复后的模型"""
    print("🧪 测试修复后的模型...")
    
    # 测试嵌入模型
    try:
        from sentence_transformers import SentenceTransformer
        embed_model = SentenceTransformer('./models/bge-base-zh')
        
        test_text = "这是一个测试文本"
        embedding = embed_model.encode([test_text])
        print(f"✅ 嵌入模型测试成功，向量维度: {embedding.shape}")
        
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        return False
    
    # 测试重排模型（如果修复成功）
    reranker_path = './models/bge-reranker-base'
    if os.path.exists(f"{reranker_path}/tokenizer.json"):
        try:
            from transformers import AutoTokenizer, AutoModel
            tokenizer = AutoTokenizer.from_pretrained(reranker_path)
            model = AutoModel.from_pretrained(reranker_path)
            print("✅ 重排模型测试成功")
            
        except Exception as e:
            print(f"⚠️  重排模型仍有问题: {e}")
            print("   将使用简化重排实现")
    
    # 测试简化重排器
    try:
        import sys
        sys.path.append('backend')
        from simple_reranker import SimpleReranker
        
        reranker = SimpleReranker()
        
        query = "什么是机器学习？"
        passages = [
            "机器学习是人工智能的一个分支",
            "深度学习是机器学习的子领域", 
            "今天天气很好"
        ]
        
        reranked = reranker.rerank(query, passages, top_k=2)
        scores = reranker.get_scores(query, passages)
        
        print(f"✅ 简化重排器测试成功")
        print(f"   原始文档: {len(passages)}个")
        print(f"   重排结果: {len(reranked)}个")
        print(f"   相似度分数: {[f'{s:.3f}' for s in scores]}")
        
    except Exception as e:
        print(f"❌ 简化重排器测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 修复重排模型配置")
    print("="*50)
    
    # 步骤1: 尝试修复tokenizer文件
    print("\n步骤1: 修复tokenizer文件")
    print("-"*30)
    tokenizer_fixed = download_missing_tokenizer_files()
    
    # 步骤2: 创建替代重排实现
    print("\n步骤2: 创建替代重排实现")
    print("-"*30)
    alternative_created = create_alternative_reranker()
    
    # 步骤3: 测试所有模型
    print("\n步骤3: 测试模型")
    print("-"*30)
    test_success = test_fixed_models()
    
    # 总结
    print("\n" + "="*50)
    print("📊 修复结果总结")
    print("="*50)
    print(f"✅ 嵌入模型: 正常工作")
    print(f"{'✅' if tokenizer_fixed else '⚠️ '} 重排模型: {'修复成功' if tokenizer_fixed else '使用替代方案'}")
    print(f"✅ 简化重排器: 已创建")
    print(f"{'✅' if test_success else '❌'} 整体测试: {'通过' if test_success else '失败'}")
    
    if test_success:
        print("\n🎉 模型配置修复完成！")
        print("💡 建议:")
        print("   1. 重启后端服务以加载本地模型")
        print("   2. 运行测试验证功能正常")
        print("   3. 如需更精确的重排，可稍后配置完整的重排模型")
    else:
        print("\n❌ 修复过程中遇到问题，请检查错误信息")
    
    return test_success

if __name__ == "__main__":
    main()
