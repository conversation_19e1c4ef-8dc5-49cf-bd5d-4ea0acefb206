# RAG应用集成测试文档

## 📋 测试概述

本测试套件为RAG（检索增强生成）应用提供全面的集成测试，包括文件上传、问答功能和性能测试。

## 🏗️ 测试架构

```
tests/
├── conftest.py              # pytest配置和fixtures
├── pytest.ini              # pytest配置文件
├── requirements.txt         # 测试依赖
├── run_tests.py            # 测试运行脚本
├── generate_test_pdf.py    # 测试PDF生成器
├── test_01_file_upload.py  # 文件上传测试
├── test_02_rag_qa.py       # RAG问答测试
├── test_03_performance.py  # 性能压力测试
├── data/                   # 测试数据目录
└── reports/                # 测试报告目录
```

## 🧪 测试用例

### 测试1：文件上传和Milvus存储验证
- **文件**: `test_01_file_upload.py`
- **目标**: 验证PDF文件上传、解析、分块和向量存储功能
- **测试场景**:
  - ✅ 成功上传PDF文件
  - ✅ 拒绝非PDF文件
  - ✅ 处理空文件错误
  - ✅ 验证Milvus存储
  - ✅ 多文件上传

### 测试2：端到端RAG问答流程
- **文件**: `test_02_rag_qa.py`
- **目标**: 验证完整的RAG问答流程
- **测试场景**:
  - ✅ 简单问答
  - ✅ 特定内容问题
  - ✅ 带历史记录的对话
  - ✅ 错误处理（空问题、无效JSON等）
  - ✅ 长问题处理
  - ✅ 不存在内容的问题

### 测试3：性能压力测试
- **文件**: `test_03_performance.py`
- **目标**: 验证系统在高负载下的性能表现
- **测试场景**:
  - ✅ 50并发问答请求
  - ✅ 10并发文件上传
  - ✅ 响应时间分析
  - ✅ 成功率统计

## 🚀 快速开始

### 1. 环境准备

确保后端服务正在运行：
```bash
cd backend
python main.py
```

### 2. 安装测试依赖

```bash
pip install -r tests/requirements.txt
```

### 3. 运行所有测试

```bash
python tests/run_tests.py
```

### 4. 运行特定测试

```bash
# 只运行文件上传测试
python tests/run_tests.py --test upload

# 只运行问答测试
python tests/run_tests.py --test qa

# 只运行性能测试
python tests/run_tests.py --test performance
```

## 🔧 高级用法

### 使用pytest直接运行

```bash
# 运行所有测试
pytest tests/

# 运行特定测试文件
pytest tests/test_01_file_upload.py -v

# 运行特定测试方法
pytest tests/test_02_rag_qa.py::TestRAGQA::test_simple_question_answer -v

# 生成HTML报告
pytest tests/ --html=reports/test_report.html --self-contained-html
```

### 配置选项

```bash
# 指定后端URL
python tests/run_tests.py --backend-url http://*************:8000

# 跳过后端服务检查
python tests/run_tests.py --skip-backend-check

# 详细输出
python tests/run_tests.py --verbose

# 不生成报告
python tests/run_tests.py --no-report
```

## 📊 测试报告

测试完成后，报告将保存在 `tests/reports/` 目录：

- **HTML报告**: 包含详细的测试结果、错误信息和统计数据
- **JSON报告**: 机器可读的测试结果数据

## 🔍 故障排除

### 常见问题

1. **后端服务连接失败**
   ```
   解决方案：确保后端服务在正确端口运行，检查防火墙设置
   ```

2. **Milvus连接失败**
   ```
   解决方案：检查Milvus服务状态，验证连接参数
   ```

3. **测试PDF生成失败**
   ```
   解决方案：确保安装了reportlab依赖
   pip install reportlab
   ```

4. **并发测试失败**
   ```
   解决方案：检查系统资源，可能需要调整并发数量
   ```

### 调试技巧

1. **启用详细日志**:
   ```bash
   pytest tests/ -v -s --log-cli-level=DEBUG
   ```

2. **运行单个测试**:
   ```bash
   pytest tests/test_01_file_upload.py::TestFileUpload::test_upload_pdf_success -v -s
   ```

3. **保留测试数据**:
   测试数据保存在 `tests/data/` 目录，可以手动检查生成的PDF文件

## 📈 性能基准

### 预期性能指标

- **文件上传**: < 30秒/文件
- **问答响应**: < 10秒平均响应时间
- **并发处理**: 80%+ 成功率（50并发）
- **95%分位数**: < 15秒响应时间

### 性能优化建议

1. **向量检索优化**: 调整Milvus索引参数
2. **模型推理优化**: 使用GPU加速或模型量化
3. **并发控制**: 实现请求队列和限流机制
4. **缓存策略**: 缓存常见问题的答案

## 🔄 持续集成

### GitHub Actions示例

```yaml
name: Integration Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: |
          pip install -r backend/requirements.txt
          pip install -r tests/requirements.txt
      - name: Start backend
        run: |
          cd backend && python main.py &
          sleep 10
      - name: Run tests
        run: python tests/run_tests.py --skip-backend-check
```

## 📝 贡献指南

### 添加新测试

1. 在相应的测试文件中添加测试方法
2. 使用描述性的测试名称
3. 添加适当的断言和错误消息
4. 更新本文档

### 测试最佳实践

1. **独立性**: 每个测试应该独立运行
2. **可重复性**: 测试结果应该一致
3. **清理**: 测试后清理临时数据
4. **文档**: 为复杂测试添加注释

## 📞 支持

如有问题或建议，请：
1. 查看故障排除部分
2. 检查测试日志和报告
3. 提交Issue或联系开发团队
