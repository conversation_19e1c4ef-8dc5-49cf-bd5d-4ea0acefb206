[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    --color=yes

# 标记定义
markers =
    slow: 标记为慢速测试
    integration: 集成测试
    performance: 性能测试
    upload: 文件上传测试
    qa: 问答测试
    
# 最小版本要求
minversion = 6.0

# 测试超时（秒）
timeout = 300

# 并行测试配置
# 如果安装了pytest-xdist，可以启用并行测试
# addopts = -n auto

# 覆盖率配置（如果需要）
# addopts = --cov=backend --cov-report=html --cov-report=term

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
