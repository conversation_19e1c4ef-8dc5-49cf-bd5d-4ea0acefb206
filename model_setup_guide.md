# 模型配置指南

## 1. BAAI/bge-base-zh 嵌入模型配置

### 下载步骤：
1. 访问Hugging Face模型仓库：https://huggingface.co/BAAI/bge-base-zh
2. 点击"Files and versions"选项卡
3. 下载以下文件：
   - `pytorch_model.bin`
   - `config.json`
   - `special_tokens_map.json`
   - `tokenizer_config.json`
   - `vocab.txt`

### 配置步骤：
1. 在项目根目录创建模型存储目录：
   ```bash
   mkdir models/bge-base-zh
   ```
2. 将下载的文件移动到该目录：
   ```
   mv /path/to/downloaded/files/* models/bge-base-zh/
   ```

## 2. bge-reranker-base 重排模型配置

### 下载步骤：
1. 访问Hugging Face模型仓库：https://huggingface.co/BAAI/bge-reranker-base
2. 下载以下文件：
   - `pytorch_model.bin`
   - `config.json`
   - `special_tokens_map.json`
   - `tokenizer_config.json`

### 配置步骤：
1. 创建模型存储目录：
   ```bash
   mkdir models/bge-reranker-base
   ```
2. 移动下载的文件：
   ```
   mv /path/to/downloaded/files/* models/bge-reranker-base/
   ```

## 3. 模型集成

在代码中加载模型：
```python
from transformers import AutoModel, AutoTokenizer

# 加载嵌入模型
embed_model = AutoModel.from_pretrained('./models/bge-base-zh')
embed_tokenizer = AutoTokenizer.from_pretrained('./models/bge-base-zh')

# 加载重排模型
rerank_model = AutoModel.from_pretrained('./models/bge-reranker-base')
rerank_tokenizer = AutoTokenizer.from_pretrained('./models/bge-reranker-base')
```

## 4. 验证模型加载

创建测试脚本 `test_model_loading.py`：
```python
import torch
from transformers import AutoModel

def test_model_loading():
    # 测试嵌入模型
    try:
        model = AutoModel.from_pretrained('./models/bge-base-zh')
        print("✅ bge-base-zh 模型加载成功")
        print(f"模型架构: {type(model).__name__}")
        print(f"参数量: {sum(p.numel() for p in model.parameters()):,}")
    except Exception as e:
        print(f"❌ bge-base-zh 模型加载失败: {str(e)}")
    
    # 测试重排模型
    try:
        model = AutoModel.from_pretrained('./models/bge-reranker-base')
        print("✅ bge-reranker-base 模型加载成功")
        print(f"模型架构: {type(model).__name__}")
    except Exception as e:
        print(f"❌ bge-reranker-base 模型加载失败: {str(e)}")

if __name__ == "__main__":
    test_model_loading()
```

运行测试：
```bash
python test_model_loading.py
```

预期输出：
```
✅ bge-base-zh 模型加载成功
模型架构: BertModel
参数量: 110,104,832
✅ bge-reranker-base 模型加载成功
模型架构: BertModel
## 5. Milvus向量数据库配置

### 安装说明（Docker部署）
```bash
# 拉取最新Milvus镜像
docker pull milvusdb/milvus:latest

# 创建持久化存储目录
mkdir -p milvus/conf milvus/data milvus/logs

# 下载默认配置文件
wget https://raw.githubusercontent.com/milvus-io/milvus/master/configs/milvus.yaml -O milvus/conf/milvus.yaml

# 启动Milvus容器
docker run -d \
  --name milvus-standalone \
  -p 19530:19530 \
  -p 9091:9091 \
  -v $PWD/milvus/data:/var/lib/milvus/data \
  -v $PWD/milvus/conf:/var/lib/milvus/conf \
  -v $PWD/milvus/logs:/var/lib/milvus/logs \
  milvusdb/milvus:latest
```

### 连接参数示例
```python
from pymilvus import connections

# 连接Milvus服务器
connections.connect(
    alias="default", 
    host='localhost',  # Milvus服务地址
    port='19530'       # Milvus服务端口
)

# 指定集合名称
collection_name = "document_vectors"
```

### 索引配置建议
- 对于768维向量（如bge-base-zh生成），推荐IVF_FLAT索引：
```python
index_params = {
    "index_type": "IVF_FLAT",
    "metric_type": "L2", 
    "params": {"nlist": 128}
}
```
- 对于大规模数据(>100万条)，建议使用IVF_PQ索引：
```python
index_params = {
    "index_type": "IVF_PQ",
    "metric_type": "L2",
    "params": {"nlist": 1024, "m": 16, "nbits": 8}
}
```

## 6. Qwen3-4B大模型调用

### API端点设置
```python
API_ENDPOINT = "http://api.qwen.ai/v1/completions"  # 官方API端点
# 或本地部署端点: "http://localhost:8000/v1/completions"
```

### 认证方式
- 在请求头中添加API密钥：
```python
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}
```

### 请求/响应格式示例
请求示例：
```json
{
  "model": "qwen3-4b",
  "prompt": "请解释量子计算的基本原理",
  "max_tokens": 500,
  "temperature": 0.7,
  "top_p": 0.9
}
```

响应示例：
```json
{
  "id": "cmpl-3Q7GtWfXvJZK9gY4XwZ7H",
  "object": "text_completion",
  "created": 1687952420,
  "model": "qwen3-4b",
  "choices": [
    {
      "text": "量子计算基于量子力学原理...",
      "index": 0,
      "logprobs": null,
      "finish_reason": "length"
    }
  ],
  "usage": {
    "prompt_tokens": 25,
    "completion_tokens": 215,
    "total_tokens": 240
  }
}
```